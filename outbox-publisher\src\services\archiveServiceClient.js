/**
 * Archive Service Client
 * HTTP client for communicating with Archive Service API
 */

const axios = require('axios');
const logger = require('../utils/logger');

class ArchiveServiceClient {
  constructor() {
    this.baseURL = process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002';
    this.apiKey = process.env.ARCHIVE_SERVICE_API_KEY || 'your-service-api-key';
    this.timeout = parseInt(process.env.ARCHIVE_SERVICE_TIMEOUT || '10000');
    
    // Create axios instance
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-Service-Auth': this.apiKey,
        'User-Agent': 'outbox-publisher/1.0.0'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('Archive Service API request', {
          method: config.method.toUpperCase(),
          url: config.url,
          data: config.data ? Object.keys(config.data) : undefined
        });
        return config;
      },
      (error) => {
        logger.error('Archive Service API request error', {
          error: error.message
        });
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('Archive Service API response', {
          status: response.status,
          url: response.config.url,
          method: response.config.method.toUpperCase()
        });
        return response;
      },
      (error) => {
        logger.error('Archive Service API response error', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          url: error.config?.url,
          method: error.config?.method?.toUpperCase(),
          error: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Mark outbox event as processed
   * @param {String} eventId - Event ID
   */
  async markEventAsProcessed(eventId) {
    try {
      const response = await this.client.put('/outbox/events/processed', {
        eventIds: [eventId]
      });

      logger.debug('Event marked as processed', {
        eventId,
        response: response.data
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to mark event as processed', {
        eventId,
        error: error.message,
        status: error.response?.status
      });
      throw this.enhanceError(error, 'mark_processed');
    }
  }

  /**
   * Mark outbox event as failed
   * @param {String} eventId - Event ID
   * @param {String} errorMessage - Error message
   * @param {Boolean} incrementRetry - Whether to increment retry count
   */
  async markEventAsFailed(eventId, errorMessage, incrementRetry = true) {
    try {
      const response = await this.client.put(`/outbox/events/${eventId}/failed`, {
        eventId,
        errorMessage,
        incrementRetry
      });

      logger.debug('Event marked as failed', {
        eventId,
        errorMessage,
        incrementRetry,
        response: response.data
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to mark event as failed', {
        eventId,
        errorMessage,
        error: error.message,
        status: error.response?.status
      });
      throw this.enhanceError(error, 'mark_failed');
    }
  }

  /**
   * Get unprocessed events
   * @param {Object} options - Query options
   */
  async getUnprocessedEvents(options = {}) {
    try {
      const params = {
        limit: options.limit || 50,
        offset: options.offset || 0
      };

      if (options.eventType) {
        params.eventType = options.eventType;
      }

      const response = await this.client.get('/outbox/events/unprocessed', { params });

      logger.debug('Retrieved unprocessed events', {
        count: response.data.data.count,
        params
      });

      return response.data.data.events;
    } catch (error) {
      logger.error('Failed to get unprocessed events', {
        options,
        error: error.message,
        status: error.response?.status
      });
      throw this.enhanceError(error, 'get_unprocessed');
    }
  }

  /**
   * Get retryable events
   * @param {Object} options - Query options
   */
  async getRetryableEvents(options = {}) {
    try {
      const params = {
        limit: options.limit || 50,
        minRetryDelay: options.minRetryDelay || 60000
      };

      const response = await this.client.get('/outbox/events/retryable', { params });

      logger.debug('Retrieved retryable events', {
        count: response.data.data.count,
        params
      });

      return response.data.data.events;
    } catch (error) {
      logger.error('Failed to get retryable events', {
        options,
        error: error.message,
        status: error.response?.status
      });
      throw this.enhanceError(error, 'get_retryable');
    }
  }

  /**
   * Get outbox statistics
   */
  async getOutboxStats() {
    try {
      const response = await this.client.get('/outbox/stats');

      logger.debug('Retrieved outbox stats', {
        stats: response.data.data
      });

      return response.data.data;
    } catch (error) {
      logger.error('Failed to get outbox stats', {
        error: error.message,
        status: error.response?.status
      });
      throw this.enhanceError(error, 'get_stats');
    }
  }

  /**
   * Clean up old events
   * @param {Number} olderThanDays - Remove events older than this many days
   */
  async cleanupOldEvents(olderThanDays = 30) {
    try {
      const response = await this.client.delete('/outbox/cleanup', {
        data: { olderThanDays }
      });

      logger.info('Old events cleaned up', {
        deletedCount: response.data.data.deletedCount,
        olderThanDays
      });

      return response.data.data.deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup old events', {
        olderThanDays,
        error: error.message,
        status: error.response?.status
      });
      throw this.enhanceError(error, 'cleanup');
    }
  }

  /**
   * Health check for Archive Service
   */
  async healthCheck() {
    try {
      const response = await this.client.get('/outbox/health');

      return {
        status: 'healthy',
        archiveService: response.data.data,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        statusCode: error.response?.status,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Batch mark events as processed
   * @param {Array} eventIds - Array of event IDs
   */
  async batchMarkAsProcessed(eventIds) {
    try {
      const response = await this.client.put('/outbox/events/processed', {
        eventIds
      });

      logger.info('Batch mark as processed completed', {
        totalEvents: eventIds.length,
        results: response.data.data
      });

      return response.data.data;
    } catch (error) {
      logger.error('Failed to batch mark as processed', {
        eventCount: eventIds.length,
        error: error.message,
        status: error.response?.status
      });
      throw this.enhanceError(error, 'batch_mark_processed');
    }
  }

  /**
   * Enhance error with additional context
   * @param {Error} error - Original error
   * @param {String} operation - Operation that failed
   */
  enhanceError(error, operation) {
    const enhancedError = new Error(`Archive Service ${operation} failed: ${error.message}`);
    enhancedError.originalError = error;
    enhancedError.operation = operation;
    enhancedError.statusCode = error.response?.status;
    enhancedError.isRetryable = this.isRetryableError(error);
    
    return enhancedError;
  }

  /**
   * Determine if an error is retryable
   * @param {Error} error - Error to check
   */
  isRetryableError(error) {
    const status = error.response?.status;
    
    // Non-retryable client errors
    if (status >= 400 && status < 500 && status !== 429) {
      return false;
    }
    
    // Retryable errors: 5xx, 429, network errors
    if (status >= 500 || status === 429 || !status) {
      return true;
    }
    
    return false;
  }

  /**
   * Get client statistics
   */
  getStats() {
    return {
      baseURL: this.baseURL,
      timeout: this.timeout,
      timestamp: new Date().toISOString()
    };
  }
}

// Create singleton instance
const archiveServiceClient = new ArchiveServiceClient();

module.exports = archiveServiceClient;
