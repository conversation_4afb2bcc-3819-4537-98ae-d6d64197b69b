/**
 * OutboxEvent Model for Outbox Publisher
 * Sequelize model for archive.outbox_events table
 * Read-only access for processing outbox events
 */

const { DataTypes, Op } = require('sequelize');
const { sequelize } = require('../config/database');

const OutboxEvent = sequelize.define('OutboxEvent', {
  id: {
    type: DataTypes.UUID,
    primaryKey: true,
    allowNull: false
  },
  aggregate_id: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'aggregate_id'
  },
  aggregate_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'aggregate_type'
  },
  event_type: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'event_type'
  },
  event_version: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'event_version'
  },
  payload: {
    type: DataTypes.JSONB,
    allowNull: false
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'created_at'
  },
  processed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'processed_at'
  },
  failed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'failed_at'
  },
  retry_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'retry_count'
  },
  max_retries: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'max_retries'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message'
  },
  routing_key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'routing_key'
  },
  exchange: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'exchange'
  }
}, {
  tableName: 'outbox_events',
  schema: 'archive',
  timestamps: false, // We don't manage timestamps
  underscored: true
});

/**
 * Get unprocessed events ready for publishing
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of unprocessed events
 */
OutboxEvent.getUnprocessedEvents = async function(options = {}) {
  const { limit = 50, offset = 0, eventType = null } = options;
  
  const whereClause = {
    processed_at: null,
    failed_at: null
  };
  
  if (eventType) {
    whereClause.event_type = eventType;
  }
  
  return await this.findAll({
    where: whereClause,
    order: [['created_at', 'ASC']],
    limit,
    offset
  });
};

/**
 * Get events that can be retried
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of retryable events
 */
OutboxEvent.getRetryableEvents = async function(options = {}) {
  const { limit = 50, minRetryDelay = 60000 } = options; // 1 minute default delay
  
  const retryAfter = new Date(Date.now() - minRetryDelay);
  
  return await this.findAll({
    where: {
      processed_at: null,
      failed_at: null,
      retry_count: {
        [Op.lt]: sequelize.col('max_retries')
      },
      created_at: {
        [Op.lt]: retryAfter
      }
    },
    order: [['created_at', 'ASC']],
    limit
  });
};

/**
 * Get events by status for monitoring
 * @returns {Promise<Object>} Event counts by status
 */
OutboxEvent.getEventCounts = async function() {
  const [results] = await sequelize.query(`
    SELECT
      COUNT(*) as total_events,
      COUNT(CASE WHEN processed_at IS NOT NULL THEN 1 END) as processed,
      COUNT(CASE WHEN processed_at IS NULL AND failed_at IS NULL THEN 1 END) as pending,
      COUNT(CASE WHEN failed_at IS NOT NULL THEN 1 END) as failed,
      COUNT(CASE WHEN retry_count > 0 AND processed_at IS NULL AND failed_at IS NULL THEN 1 END) as retrying
    FROM archive.outbox_events
  `, {
    type: sequelize.QueryTypes.SELECT
  });
  
  return results[0];
};

/**
 * Get oldest unprocessed event age in minutes
 * @returns {Promise<Number|null>} Age in minutes or null if no unprocessed events
 */
OutboxEvent.getOldestUnprocessedAge = async function() {
  const [results] = await sequelize.query(`
    SELECT 
      EXTRACT(EPOCH FROM (NOW() - MIN(created_at))) / 60 as age_minutes
    FROM archive.outbox_events
    WHERE processed_at IS NULL AND failed_at IS NULL
  `, {
    type: sequelize.QueryTypes.SELECT
  });
  
  return results[0]?.age_minutes || null;
};

/**
 * Instance methods
 */
OutboxEvent.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  
  // Convert timestamps to ISO strings for consistency
  if (values.created_at) {
    values.created_at = values.created_at.toISOString();
  }
  if (values.processed_at) {
    values.processed_at = values.processed_at.toISOString();
  }
  if (values.failed_at) {
    values.failed_at = values.failed_at.toISOString();
  }
  
  return values;
};

/**
 * Check if event can be retried
 */
OutboxEvent.prototype.canRetry = function() {
  return this.retry_count < this.max_retries && !this.processed_at && !this.failed_at;
};

/**
 * Check if event is permanently failed
 */
OutboxEvent.prototype.isPermanentlyFailed = function() {
  return this.failed_at !== null || this.retry_count >= this.max_retries;
};

/**
 * Get retry delay in milliseconds (exponential backoff)
 */
OutboxEvent.prototype.getRetryDelay = function() {
  const baseDelay = parseInt(process.env.OUTBOX_RETRY_DELAY_BASE || '1000');
  const maxDelay = parseInt(process.env.OUTBOX_RETRY_DELAY_MAX || '60000');
  
  const delay = Math.min(baseDelay * Math.pow(2, this.retry_count), maxDelay);
  return delay;
};

/**
 * Check if event is ready for retry
 */
OutboxEvent.prototype.isReadyForRetry = function() {
  if (!this.canRetry()) {
    return false;
  }
  
  const retryDelay = this.getRetryDelay();
  const retryAfter = new Date(this.created_at.getTime() + retryDelay);
  
  return new Date() >= retryAfter;
};

module.exports = OutboxEvent;
