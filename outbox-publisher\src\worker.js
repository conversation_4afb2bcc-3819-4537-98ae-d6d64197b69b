/**
 * Outbox Publisher Worker - Main Entry Point
 * 
 * This worker polls the outbox table for unprocessed events,
 * publishes them to RabbitMQ, and marks them as processed.
 * Implements the Outbox Pattern for reliable event publishing.
 */

// Load environment variables
require('dotenv').config();

// Import dependencies
const logger = require('./utils/logger');
const { initialize: initializeDatabase, close: closeDatabase } = require('./config/database');
const rabbitmqPublisher = require('./services/rabbitmqPublisher');
const OutboxProcessor = require('./services/outboxProcessor');
const archiveServiceClient = require('./services/archiveServiceClient');

// Global state
let outboxProcessor = null;
let isShuttingDown = false;
let healthCheckServer = null;

// Log worker startup
logger.info('Outbox Publisher Worker starting up', {
  env: process.env.NODE_ENV,
  pollInterval: process.env.OUTBOX_POLL_INTERVAL,
  batchSize: process.env.OUTBOX_BATCH_SIZE,
  version: process.env.npm_package_version || '1.0.0'
});

/**
 * Initialize all services
 */
async function initializeServices() {
  try {
    logger.info('Initializing services...');

    // Initialize database connection
    await initializeDatabase();
    logger.info('Database initialized');

    // Initialize RabbitMQ publisher
    await rabbitmqPublisher.initialize();
    logger.info('RabbitMQ publisher initialized');

    // Create and start outbox processor
    outboxProcessor = new OutboxProcessor();
    await outboxProcessor.start();
    logger.info('Outbox processor started');

    // Start health check server if enabled
    if (process.env.HEALTH_CHECK_ENABLED === 'true') {
      await startHealthCheckServer();
    }

    logger.info('All services initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize services', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Start health check HTTP server
 */
async function startHealthCheckServer() {
  const express = require('express');
  const app = express();
  const port = parseInt(process.env.HEALTH_CHECK_PORT || '3003');

  app.use(express.json());

  // Health check endpoint
  app.get('/health', async (req, res) => {
    try {
      const [
        processorHealth,
        publisherHealth,
        archiveHealth,
        dbHealth
      ] = await Promise.all([
        outboxProcessor.getHealthStatus(),
        rabbitmqPublisher.healthCheck(),
        archiveServiceClient.healthCheck(),
        require('./config/database').healthCheck()
      ]);

      const overallHealth = 
        processorHealth.healthy && 
        publisherHealth.status === 'healthy' && 
        archiveHealth.status === 'healthy' && 
        dbHealth.status === 'healthy';

      const response = {
        status: overallHealth ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        services: {
          processor: processorHealth,
          publisher: publisherHealth,
          archiveService: archiveHealth,
          database: dbHealth
        }
      };

      res.status(overallHealth ? 200 : 503).json(response);
    } catch (error) {
      logger.error('Health check failed', { error: error.message });
      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Metrics endpoint
  app.get('/metrics', async (req, res) => {
    try {
      const stats = {
        processor: outboxProcessor.getProcessingStats(),
        publisher: rabbitmqPublisher.getStats(),
        archiveService: archiveServiceClient.getStats(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      };

      res.json(stats);
    } catch (error) {
      logger.error('Metrics collection failed', { error: error.message });
      res.status(500).json({
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Ready endpoint (for Kubernetes readiness probe)
  app.get('/ready', (req, res) => {
    if (isShuttingDown) {
      return res.status(503).json({
        status: 'shutting_down',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      status: 'ready',
      timestamp: new Date().toISOString()
    });
  });

  // Start server
  healthCheckServer = app.listen(port, () => {
    logger.info(`Health check server running on port ${port}`);
  });
}

/**
 * Graceful shutdown handler
 */
async function gracefulShutdown(signal) {
  if (isShuttingDown) {
    logger.warn('Shutdown already in progress, forcing exit');
    process.exit(1);
  }

  isShuttingDown = true;
  logger.info(`Received ${signal}, starting graceful shutdown`);

  const shutdownTimeout = parseInt(process.env.WORKER_SHUTDOWN_TIMEOUT || '30000');
  
  // Set a timeout for forced shutdown
  const forceShutdownTimer = setTimeout(() => {
    logger.error('Graceful shutdown timeout, forcing exit');
    process.exit(1);
  }, shutdownTimeout);

  try {
    // Stop accepting new work
    if (outboxProcessor) {
      logger.info('Stopping outbox processor...');
      await outboxProcessor.stop();
      logger.info('Outbox processor stopped');
    }

    // Close RabbitMQ connection
    if (rabbitmqPublisher) {
      logger.info('Closing RabbitMQ publisher...');
      await rabbitmqPublisher.close();
      logger.info('RabbitMQ publisher closed');
    }

    // Close health check server
    if (healthCheckServer) {
      logger.info('Closing health check server...');
      await new Promise((resolve) => {
        healthCheckServer.close(resolve);
      });
      logger.info('Health check server closed');
    }

    // Close database connection
    logger.info('Closing database connection...');
    await closeDatabase();
    logger.info('Database connection closed');

    // Clear the force shutdown timer
    clearTimeout(forceShutdownTimer);

    logger.info('Graceful shutdown completed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown', {
      error: error.message,
      stack: error.stack
    });
    clearTimeout(forceShutdownTimer);
    process.exit(1);
  }
}

/**
 * Main function to start the worker
 */
async function startWorker() {
  try {
    // Initialize all services
    await initializeServices();

    // Log successful startup
    logger.info('Outbox Publisher Worker ready - processing events', {
      processId: process.pid,
      nodeVersion: process.version,
      platform: process.platform
    });

    // Setup heartbeat for monitoring
    const heartbeatInterval = parseInt(process.env.HEARTBEAT_INTERVAL || '300000'); // 5 minutes default
    setInterval(() => {
      if (!isShuttingDown) {
        logger.info('Worker heartbeat', {
          status: 'running',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          stats: outboxProcessor ? outboxProcessor.getProcessingStats() : null
        });
      }
    }, heartbeatInterval);

  } catch (error) {
    logger.error('Failed to start Outbox Publisher Worker', {
      error: error.message,
      stack: error.stack
    });

    // Exit with error
    process.exit(1);
  }
}

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', {
    error: error.message,
    stack: error.stack
  });
  
  // Attempt graceful shutdown
  gracefulShutdown('UNCAUGHT_EXCEPTION').finally(() => {
    process.exit(1);
  });
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled promise rejection', {
    reason: reason instanceof Error ? reason.message : reason,
    stack: reason instanceof Error ? reason.stack : undefined,
    promise: promise.toString()
  });
  
  // Attempt graceful shutdown
  gracefulShutdown('UNHANDLED_REJECTION').finally(() => {
    process.exit(1);
  });
});

// Start the worker
if (require.main === module) {
  startWorker();
}

module.exports = {
  startWorker,
  gracefulShutdown,
  initializeServices
};
