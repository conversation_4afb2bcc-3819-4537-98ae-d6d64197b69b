/**
 * RabbitMQ Configuration for Outbox Publisher
 * Handles connection and publishing to RabbitMQ
 */

const amqp = require('amqplib');
const logger = require('../utils/logger');

// RabbitMQ configuration
const config = {
  url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
  exchange: process.env.RABBITMQ_EXCHANGE || 'atma_events',
  routingKey: process.env.RABBITMQ_ROUTING_KEY || 'assessment.analysis',
  queue: process.env.RABBITMQ_QUEUE || 'assessment_analysis_queue',
  options: {
    durable: true,
    persistent: true,
    heartbeat: parseInt(process.env.RABBITMQ_HEARTBEAT || '60'),
    connectionTimeout: parseInt(process.env.RABBITMQ_CONNECTION_TIMEOUT || '10000')
  }
};

let connection = null;
let channel = null;
let isConnected = false;

/**
 * Initialize RabbitMQ connection and setup exchange/queue
 */
const initialize = async () => {
  try {
    logger.info('Connecting to RabbitMQ...', { url: config.url });
    
    // Create connection with options
    connection = await amqp.connect(config.url, {
      heartbeat: config.options.heartbeat,
      connectionTimeout: config.options.connectionTimeout
    });

    // Handle connection events
    connection.on('close', () => {
      logger.warn('RabbitMQ connection closed');
      isConnected = false;
      setTimeout(reconnect, parseInt(process.env.RABBITMQ_RECONNECT_DELAY || '5000'));
    });

    connection.on('error', (err) => {
      logger.error('RabbitMQ connection error', { error: err.message });
      isConnected = false;
      setTimeout(reconnect, parseInt(process.env.RABBITMQ_RECONNECT_DELAY || '5000'));
    });

    // Create channel
    channel = await connection.createChannel();

    // Handle channel events
    channel.on('error', (err) => {
      logger.error('RabbitMQ channel error', { error: err.message });
    });

    channel.on('close', () => {
      logger.warn('RabbitMQ channel closed');
    });

    // Setup exchange
    await channel.assertExchange(config.exchange, 'direct', {
      durable: config.options.durable
    });

    // Setup queue
    await channel.assertQueue(config.queue, {
      durable: config.options.durable,
      arguments: {
        'x-dead-letter-exchange': config.exchange,
        'x-dead-letter-routing-key': 'dlq'
      }
    });

    // Bind queue to exchange
    await channel.bindQueue(config.queue, config.exchange, config.routingKey);

    isConnected = true;

    logger.info('RabbitMQ connection established successfully', {
      exchange: config.exchange,
      queue: config.queue,
      routingKey: config.routingKey
    });

    return channel;
  } catch (error) {
    logger.error('Failed to initialize RabbitMQ', { error: error.message });
    isConnected = false;
    throw error;
  }
};

/**
 * Reconnect to RabbitMQ
 */
const reconnect = async () => {
  try {
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        logger.error('Error closing existing RabbitMQ connection', { error: err.message });
      }
    }

    await initialize();
  } catch (error) {
    logger.error('Failed to reconnect to RabbitMQ', { error: error.message });
    setTimeout(reconnect, parseInt(process.env.RABBITMQ_RECONNECT_DELAY || '5000'));
  }
};

/**
 * Get current channel
 */
const getChannel = () => {
  if (!channel || !isConnected) {
    throw new Error('RabbitMQ channel not available');
  }
  return channel;
};

/**
 * Check if RabbitMQ is connected
 */
const isConnectionHealthy = () => {
  return isConnected && channel !== null;
};

/**
 * Publish message to RabbitMQ
 */
const publishMessage = async (message, routingKey = null, options = {}) => {
  try {
    if (!isConnectionHealthy()) {
      throw new Error('RabbitMQ connection not healthy');
    }

    const messageBuffer = Buffer.from(JSON.stringify(message));
    const publishOptions = {
      persistent: config.options.persistent,
      timestamp: Date.now(),
      ...options
    };

    const published = channel.publish(
      config.exchange,
      routingKey || config.routingKey,
      messageBuffer,
      publishOptions
    );

    if (!published) {
      throw new Error('Failed to publish message to RabbitMQ');
    }

    logger.debug('Message published to RabbitMQ', {
      exchange: config.exchange,
      routingKey: routingKey || config.routingKey,
      messageId: options.messageId
    });

    return true;
  } catch (error) {
    logger.error('Failed to publish message to RabbitMQ', {
      error: error.message,
      routingKey: routingKey || config.routingKey,
      messageId: options.messageId
    });
    throw error;
  }
};

/**
 * Close RabbitMQ connection
 */
const close = async () => {
  try {
    if (channel) {
      await channel.close();
      logger.info('RabbitMQ channel closed');
    }
    
    if (connection) {
      await connection.close();
      logger.info('RabbitMQ connection closed');
    }
    
    isConnected = false;
  } catch (error) {
    logger.error('Error closing RabbitMQ connection', { error: error.message });
    throw error;
  }
};

/**
 * Health check for RabbitMQ connection
 */
const healthCheck = async () => {
  try {
    if (!isConnectionHealthy()) {
      return {
        status: 'unhealthy',
        error: 'Connection not established',
        timestamp: new Date().toISOString()
      };
    }

    // Try to get queue info as a health check
    await channel.checkQueue(config.queue);
    
    return {
      status: 'healthy',
      exchange: config.exchange,
      queue: config.queue,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

module.exports = {
  config,
  initialize,
  reconnect,
  getChannel,
  isConnectionHealthy,
  publishMessage,
  close,
  healthCheck
};
