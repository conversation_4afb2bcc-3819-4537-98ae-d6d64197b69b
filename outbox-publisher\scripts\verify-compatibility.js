/**
 * Compatibility Verification Script
 * Verifies that the Outbox Publisher generates messages compatible with Analysis Worker
 */

const path = require('path');

// Mock outbox event (similar to what would be in the database)
const mockOutboxEvent = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  aggregate_id: '987fcdeb-51a2-43d1-9f12-************',
  aggregate_type: 'assessment_job',
  event_type: 'assessment.submitted',
  event_version: 1,
  payload: {
    jobId: '987fcdeb-51a2-43d1-9f12-************',
    userId: '456789ab-cdef-1234-5678-90abcdef1234',
    userEmail: '<EMAIL>',
    assessmentData: {
      riasec: {
        realistic: 75,
        investigative: 80,
        artistic: 60,
        social: 85,
        enterprising: 70,
        conventional: 65
      },
      ocean: {
        openness: 78,
        conscientiousness: 82,
        extraversion: 75,
        agreeableness: 80,
        neuroticism: 45
      },
      viaIs: {
        creativity: 85,
        curiosity: 80,
        judgment: 75,
        loveOfLearning: 82,
        perspective: 78,
        bravery: 70,
        perseverance: 85,
        honesty: 88,
        zest: 75,
        love: 80,
        kindness: 85,
        socialIntelligence: 78,
        teamwork: 82,
        fairness: 80,
        leadership: 75,
        forgiveness: 70,
        humility: 72,
        prudence: 78,
        selfRegulation: 80,
        appreciationOfBeauty: 65,
        gratitude: 85,
        hope: 80,
        humor: 75,
        spirituality: 60
      }
    },
    assessmentName: 'AI-Driven Talent Mapping',
    timestamp: '2024-01-15T10:30:00.000Z',
    retryCount: 0
  },
  metadata: {
    correlationId: 'assessment_job-987fcdeb-51a2-43d1-9f12-************-*************',
    source: 'archive-service',
    createdBy: 'outbox-service',
    timestamp: '2024-01-15T10:30:00.000Z'
  },
  created_at: '2024-01-15T10:30:00.000Z',
  processed_at: null,
  failed_at: null,
  retry_count: 0,
  max_retries: 5,
  error_message: null,
  routing_key: 'assessment.analysis',
  exchange: 'atma_events'
};

// Import the message preparation logic
function prepareMessage(event) {
  // This is the same logic from rabbitmqPublisher.js
  if (event.event_type === 'assessment.submitted') {
    return {
      ...event.payload,
      // Add metadata from the event
      eventId: event.id,
      eventType: event.event_type,
      eventVersion: event.event_version,
      aggregateId: event.aggregate_id,
      aggregateType: event.aggregate_type,
      originalTimestamp: event.created_at,
      publishedAt: new Date().toISOString()
    };
  }

  // For other event types, wrap the payload
  return {
    eventId: event.id,
    eventType: event.event_type,
    eventVersion: event.event_version,
    aggregateId: event.aggregate_id,
    aggregateType: event.aggregate_type,
    payload: event.payload,
    metadata: event.metadata,
    timestamp: event.created_at,
    publishedAt: new Date().toISOString()
  };
}

// Mock Analysis Worker validation (simplified version)
const Joi = require('joi');

const jobMessageSchema = Joi.object({
  jobId: Joi.string().uuid().required(),
  userId: Joi.string().uuid().required(),
  userEmail: Joi.string().email().required(),
  assessmentData: Joi.object().required(),
  assessmentName: Joi.string().valid('AI-Driven Talent Mapping', 'AI-Based IQ Test', 'Custom Assessment').default('AI-Driven Talent Mapping').optional(),
  timestamp: Joi.string().isoDate().required(),
  retryCount: Joi.number().min(0).default(0)
});

function validateJobMessage(message) {
  try {
    const { error, value } = jobMessageSchema.validate(message, { abortEarly: false });
    
    if (error) {
      return {
        isValid: false,
        error: error.details.map(detail => detail.message).join(', '),
        value: null
      };
    }
    
    return {
      isValid: true,
      error: null,
      value
    };
  } catch (err) {
    return {
      isValid: false,
      error: `Validation error: ${err.message}`,
      value: null
    };
  }
}

// Run the verification
console.log('🔍 Verifying Outbox Publisher <-> Analysis Worker Compatibility\n');

console.log('1. Mock Outbox Event:');
console.log(JSON.stringify(mockOutboxEvent, null, 2));
console.log('\n' + '='.repeat(80) + '\n');

console.log('2. Prepared Message (what gets sent to RabbitMQ):');
const preparedMessage = prepareMessage(mockOutboxEvent);
console.log(JSON.stringify(preparedMessage, null, 2));
console.log('\n' + '='.repeat(80) + '\n');

console.log('3. Analysis Worker Validation:');
const validationResult = validateJobMessage(preparedMessage);

if (validationResult.isValid) {
  console.log('✅ SUCCESS: Message is compatible with Analysis Worker!');
  console.log('\nValidated fields:');
  console.log(`- jobId: ${preparedMessage.jobId}`);
  console.log(`- userId: ${preparedMessage.userId}`);
  console.log(`- userEmail: ${preparedMessage.userEmail}`);
  console.log(`- assessmentData: ${Object.keys(preparedMessage.assessmentData).join(', ')}`);
  console.log(`- assessmentName: ${preparedMessage.assessmentName}`);
  console.log(`- timestamp: ${preparedMessage.timestamp}`);
  console.log(`- retryCount: ${preparedMessage.retryCount}`);
} else {
  console.log('❌ FAILURE: Message is NOT compatible with Analysis Worker!');
  console.log(`Error: ${validationResult.error}`);
}

console.log('\n' + '='.repeat(80) + '\n');

console.log('4. Additional Metadata (added by Outbox Publisher):');
console.log(`- eventId: ${preparedMessage.eventId}`);
console.log(`- eventType: ${preparedMessage.eventType}`);
console.log(`- eventVersion: ${preparedMessage.eventVersion}`);
console.log(`- aggregateId: ${preparedMessage.aggregateId}`);
console.log(`- aggregateType: ${preparedMessage.aggregateType}`);
console.log(`- originalTimestamp: ${preparedMessage.originalTimestamp}`);
console.log(`- publishedAt: ${preparedMessage.publishedAt}`);

console.log('\n✨ Verification Complete!\n');

if (validationResult.isValid) {
  console.log('🎉 The Outbox Publisher is fully compatible with the Analysis Worker.');
  console.log('   No changes are needed to the Analysis Worker.');
  process.exit(0);
} else {
  console.log('⚠️  Compatibility issues detected. Please review the message format.');
  process.exit(1);
}
