/**
 * OutboxEvent Model for Archive Service
 * Sequelize model for archive.outbox_events table
 * Implements the Outbox Pattern for reliable event publishing
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const OutboxEvent = sequelize.define('OutboxEvent', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
    allowNull: false
  },
  aggregate_id: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'aggregate_id',
    comment: 'ID of the aggregate that generated this event (e.g., job_id)'
  },
  aggregate_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'aggregate_type',
    validate: {
      isIn: [['assessment_job', 'analysis_result', 'user_profile']]
    },
    comment: 'Type of aggregate that generated this event'
  },
  event_type: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'event_type',
    validate: {
      isIn: [['assessment.submitted', 'assessment.completed', 'assessment.failed', 'result.created']]
    },
    comment: 'Type of event being published'
  },
  event_version: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    field: 'event_version',
    comment: 'Version of the event schema for backward compatibility'
  },
  payload: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'Event payload containing all necessary data for processing'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Additional metadata about the event (correlation IDs, source info, etc.)'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  processed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'processed_at',
    comment: 'Timestamp when event was successfully published to RabbitMQ'
  },
  failed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'failed_at',
    comment: 'Timestamp when event processing permanently failed'
  },
  retry_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'retry_count',
    comment: 'Number of times this event has been retried'
  },
  max_retries: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 5,
    field: 'max_retries',
    comment: 'Maximum number of retry attempts before marking as failed'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message',
    comment: 'Last error message if processing failed'
  },
  routing_key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'routing_key',
    comment: 'RabbitMQ routing key for this event'
  },
  exchange: {
    type: DataTypes.STRING(100),
    allowNull: false,
    defaultValue: 'atma_events',
    field: 'exchange',
    comment: 'RabbitMQ exchange for this event'
  }
}, {
  tableName: 'outbox_events',
  schema: 'archive',
  timestamps: false, // We manage timestamps manually
  underscored: true,
  indexes: [
    {
      name: 'idx_outbox_events_unprocessed',
      fields: ['processed_at', 'failed_at', 'retry_count', 'max_retries'],
      where: {
        processed_at: null,
        failed_at: null
      },
      comment: 'Index for finding unprocessed events efficiently'
    },
    {
      name: 'idx_outbox_events_retry',
      fields: ['failed_at', 'retry_count', 'max_retries', 'created_at'],
      comment: 'Index for retry logic'
    },
    {
      name: 'idx_outbox_events_aggregate',
      fields: ['aggregate_type', 'aggregate_id', 'created_at'],
      comment: 'Index for finding events by aggregate'
    },
    {
      name: 'idx_outbox_events_type',
      fields: ['event_type', 'created_at'],
      comment: 'Index for finding events by type'
    },
    {
      name: 'idx_outbox_events_created',
      fields: ['created_at'],
      comment: 'Index for chronological ordering'
    }
  ]
});

/**
 * Instance methods
 */
OutboxEvent.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  
  // Convert timestamps to ISO strings for consistency
  if (values.created_at) {
    values.created_at = values.created_at.toISOString();
  }
  if (values.processed_at) {
    values.processed_at = values.processed_at.toISOString();
  }
  if (values.failed_at) {
    values.failed_at = values.failed_at.toISOString();
  }
  
  return values;
};

/**
 * Check if event can be retried
 */
OutboxEvent.prototype.canRetry = function() {
  return this.retry_count < this.max_retries && !this.processed_at && !this.failed_at;
};

/**
 * Check if event is permanently failed
 */
OutboxEvent.prototype.isPermanentlyFailed = function() {
  return this.failed_at !== null || this.retry_count >= this.max_retries;
};

/**
 * Class methods for querying events
 */

/**
 * Get unprocessed events ready for publishing
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of unprocessed events
 */
OutboxEvent.getUnprocessedEvents = async function(options = {}) {
  const { limit = 50, offset = 0, eventType = null } = options;
  
  const whereClause = {
    processed_at: null,
    failed_at: null
  };
  
  if (eventType) {
    whereClause.event_type = eventType;
  }
  
  return await this.findAll({
    where: whereClause,
    order: [['created_at', 'ASC']],
    limit,
    offset
  });
};

/**
 * Get events that can be retried
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of retryable events
 */
OutboxEvent.getRetryableEvents = async function(options = {}) {
  const { limit = 50, minRetryDelay = 60000 } = options; // 1 minute default delay
  
  const retryAfter = new Date(Date.now() - minRetryDelay);
  
  return await this.findAll({
    where: {
      processed_at: null,
      failed_at: null,
      retry_count: {
        [sequelize.Op.lt]: sequelize.col('max_retries')
      },
      created_at: {
        [sequelize.Op.lt]: retryAfter
      }
    },
    order: [['created_at', 'ASC']],
    limit
  });
};

/**
 * Get events by aggregate
 * @param {String} aggregateType - Type of aggregate
 * @param {String} aggregateId - ID of aggregate
 * @returns {Promise<Array>} Array of events for the aggregate
 */
OutboxEvent.getEventsByAggregate = async function(aggregateType, aggregateId) {
  return await this.findAll({
    where: {
      aggregate_type: aggregateType,
      aggregate_id: aggregateId
    },
    order: [['created_at', 'ASC']]
  });
};

/**
 * Get processing statistics
 * @returns {Promise<Object>} Processing statistics
 */
OutboxEvent.getProcessingStats = async function() {
  const [results] = await sequelize.query(`
    SELECT
      COUNT(*) as total_events,
      COUNT(CASE WHEN processed_at IS NOT NULL THEN 1 END) as processed,
      COUNT(CASE WHEN processed_at IS NULL AND failed_at IS NULL THEN 1 END) as pending,
      COUNT(CASE WHEN failed_at IS NOT NULL THEN 1 END) as failed,
      AVG(CASE 
        WHEN processed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (processed_at - created_at))
        ELSE NULL 
      END) as avg_processing_time_seconds,
      MAX(created_at) as latest_event,
      MIN(CASE WHEN processed_at IS NULL AND failed_at IS NULL THEN created_at END) as oldest_pending
    FROM archive.outbox_events
  `, {
    type: sequelize.QueryTypes.SELECT
  });
  
  return results[0];
};

module.exports = OutboxEvent;
