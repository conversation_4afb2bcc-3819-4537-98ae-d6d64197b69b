/**
 * Outbox Service for Archive Service
 * Handles outbox event creation, processing, and management
 * Implements the Outbox Pattern for reliable event publishing
 */

const OutboxEvent = require('../models/OutboxEvent');
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

class OutboxService {
  /**
   * Create a new outbox event
   * @param {Object} eventData - Event data
   * @param {Object} transaction - Database transaction (optional)
   * @returns {Promise<OutboxEvent>} Created outbox event
   */
  static async createEvent(eventData, transaction = null) {
    try {
      const {
        aggregateId,
        aggregateType,
        eventType,
        payload,
        routingKey,
        exchange = 'atma_events',
        metadata = {},
        eventVersion = 1,
        maxRetries = 5
      } = eventData;

      // Validate required fields
      if (!aggregateId || !aggregateType || !eventType || !payload || !routingKey) {
        throw new Error('Missing required fields for outbox event creation');
      }

      // Add correlation metadata
      const enrichedMetadata = {
        ...metadata,
        correlationId: metadata.correlationId || `${aggregateType}-${aggregateId}-${Date.now()}`,
        source: 'archive-service',
        createdBy: 'outbox-service',
        timestamp: new Date().toISOString()
      };

      const outboxEvent = await OutboxEvent.create({
        aggregate_id: aggregateId,
        aggregate_type: aggregateType,
        event_type: eventType,
        event_version: eventVersion,
        payload,
        metadata: enrichedMetadata,
        routing_key: routingKey,
        exchange,
        max_retries: maxRetries
      }, { transaction });

      logger.info('Outbox event created', {
        eventId: outboxEvent.id,
        aggregateId,
        aggregateType,
        eventType,
        routingKey,
        correlationId: enrichedMetadata.correlationId
      });

      return outboxEvent;
    } catch (error) {
      logger.error('Failed to create outbox event', {
        error: error.message,
        eventData
      });
      throw error;
    }
  }

  /**
   * Create assessment job outbox event
   * @param {String} jobId - Job ID
   * @param {String} userId - User ID
   * @param {Object} assessmentData - Assessment data
   * @param {String} assessmentName - Assessment name
   * @param {Object} transaction - Database transaction
   * @returns {Promise<OutboxEvent>} Created outbox event
   */
  static async createAssessmentJobEvent(jobId, userId, assessmentData, assessmentName, transaction) {
    const payload = {
      jobId,
      userId,
      userEmail: assessmentData.userEmail || '<EMAIL>',
      assessmentData,
      assessmentName,
      timestamp: new Date().toISOString(),
      retryCount: 0
    };

    const metadata = {
      jobId,
      userId,
      assessmentName,
      jobType: 'assessment_analysis',
      source: 'assessment-service'
    };

    return await this.createEvent({
      aggregateId: jobId,
      aggregateType: 'assessment_job',
      eventType: 'assessment.submitted',
      payload,
      routingKey: 'assessment.analysis',
      exchange: 'atma_events',
      metadata
    }, transaction);
  }

  /**
   * Get unprocessed events for publishing
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of unprocessed events
   */
  static async getUnprocessedEvents(options = {}) {
    try {
      const events = await OutboxEvent.getUnprocessedEvents(options);
      
      logger.debug('Retrieved unprocessed events', {
        count: events.length,
        options
      });

      return events;
    } catch (error) {
      logger.error('Failed to get unprocessed events', {
        error: error.message,
        options
      });
      throw error;
    }
  }

  /**
   * Get events that can be retried
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of retryable events
   */
  static async getRetryableEvents(options = {}) {
    try {
      const events = await OutboxEvent.getRetryableEvents(options);
      
      logger.debug('Retrieved retryable events', {
        count: events.length,
        options
      });

      return events;
    } catch (error) {
      logger.error('Failed to get retryable events', {
        error: error.message,
        options
      });
      throw error;
    }
  }

  /**
   * Mark event as processed
   * @param {String} eventId - Event ID
   * @returns {Promise<Boolean>} Success status
   */
  static async markAsProcessed(eventId) {
    const transaction = await sequelize.transaction();
    
    try {
      const [updatedRows] = await OutboxEvent.update({
        processed_at: new Date(),
        error_message: null
      }, {
        where: { id: eventId },
        transaction
      });

      if (updatedRows === 0) {
        throw new Error(`Outbox event not found: ${eventId}`);
      }

      await transaction.commit();

      logger.info('Outbox event marked as processed', { eventId });
      return true;
    } catch (error) {
      await transaction.rollback();
      logger.error('Failed to mark event as processed', {
        eventId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Mark event as failed
   * @param {String} eventId - Event ID
   * @param {String} errorMessage - Error message
   * @param {Boolean} incrementRetry - Whether to increment retry count
   * @returns {Promise<Boolean>} Success status
   */
  static async markAsFailed(eventId, errorMessage, incrementRetry = true) {
    const transaction = await sequelize.transaction();
    
    try {
      const event = await OutboxEvent.findByPk(eventId, { transaction });
      
      if (!event) {
        throw new Error(`Outbox event not found: ${eventId}`);
      }

      const updateData = {
        error_message: errorMessage
      };

      if (incrementRetry) {
        updateData.retry_count = event.retry_count + 1;
        
        // Mark as permanently failed if max retries exceeded
        if (updateData.retry_count >= event.max_retries) {
          updateData.failed_at = new Date();
        }
      } else {
        // Permanent failure without retry
        updateData.failed_at = new Date();
      }

      await OutboxEvent.update(updateData, {
        where: { id: eventId },
        transaction
      });

      await transaction.commit();

      logger.warn('Outbox event marked as failed', {
        eventId,
        retryCount: updateData.retry_count,
        permanentlyFailed: !!updateData.failed_at,
        errorMessage
      });

      return true;
    } catch (error) {
      await transaction.rollback();
      logger.error('Failed to mark event as failed', {
        eventId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get processing statistics
   * @returns {Promise<Object>} Processing statistics
   */
  static async getProcessingStats() {
    try {
      const stats = await OutboxEvent.getProcessingStats();
      
      logger.debug('Retrieved outbox processing stats', stats);
      
      return stats;
    } catch (error) {
      logger.error('Failed to get processing stats', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get events by aggregate
   * @param {String} aggregateType - Aggregate type
   * @param {String} aggregateId - Aggregate ID
   * @returns {Promise<Array>} Array of events
   */
  static async getEventsByAggregate(aggregateType, aggregateId) {
    try {
      const events = await OutboxEvent.getEventsByAggregate(aggregateType, aggregateId);
      
      logger.debug('Retrieved events by aggregate', {
        aggregateType,
        aggregateId,
        count: events.length
      });

      return events;
    } catch (error) {
      logger.error('Failed to get events by aggregate', {
        aggregateType,
        aggregateId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Clean up old processed events
   * @param {Number} olderThanDays - Remove events older than this many days
   * @returns {Promise<Number>} Number of deleted events
   */
  static async cleanupOldEvents(olderThanDays = 30) {
    const transaction = await sequelize.transaction();
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const deletedCount = await OutboxEvent.destroy({
        where: {
          processed_at: {
            [sequelize.Op.not]: null
          },
          created_at: {
            [sequelize.Op.lt]: cutoffDate
          }
        },
        transaction
      });

      await transaction.commit();

      logger.info('Cleaned up old outbox events', {
        deletedCount,
        olderThanDays,
        cutoffDate: cutoffDate.toISOString()
      });

      return deletedCount;
    } catch (error) {
      await transaction.rollback();
      logger.error('Failed to cleanup old events', {
        olderThanDays,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Batch process events
   * @param {Array} eventIds - Array of event IDs to process
   * @param {Function} processor - Function to process each event
   * @returns {Promise<Object>} Processing results
   */
  static async batchProcess(eventIds, processor) {
    const results = {
      processed: 0,
      failed: 0,
      errors: []
    };

    for (const eventId of eventIds) {
      try {
        const event = await OutboxEvent.findByPk(eventId);
        if (!event) {
          results.errors.push({ eventId, error: 'Event not found' });
          results.failed++;
          continue;
        }

        await processor(event);
        await this.markAsProcessed(eventId);
        results.processed++;
      } catch (error) {
        await this.markAsFailed(eventId, error.message);
        results.errors.push({ eventId, error: error.message });
        results.failed++;
      }
    }

    logger.info('Batch processing completed', {
      totalEvents: eventIds.length,
      processed: results.processed,
      failed: results.failed
    });

    return results;
  }
}

module.exports = OutboxService;
