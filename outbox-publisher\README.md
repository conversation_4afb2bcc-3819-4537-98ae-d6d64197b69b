# ATMA Outbox Publisher

The Outbox Publisher is a standalone service that implements the Outbox Pattern for reliable event publishing in the ATMA (AI-Driven Talent Mapping Assessment) system. It ensures that database writes and message publishing are atomic, preventing data inconsistency issues.

## Overview

The Outbox Pattern solves the problem of maintaining consistency between database operations and message publishing. Instead of directly publishing to RabbitMQ during business operations, events are stored in an outbox table within the same database transaction. The Outbox Publisher then reads these events and publishes them to RabbitMQ, ensuring eventual delivery even if the message broker is temporarily unavailable.

## Features

### Core Functionality
- **Reliable Event Publishing**: Guarantees that events stored in the outbox will eventually be published
- **Atomic Operations**: Database writes and event creation happen in the same transaction
- **Retry Logic**: Implements exponential backoff for failed publishing attempts
- **Circuit Breaker**: Prevents cascading failures during outages
- **Batch Processing**: Processes multiple events efficiently
- **Dead Letter Handling**: Manages permanently failed events

### Monitoring & Observability
- **Health Checks**: HTTP endpoints for health monitoring
- **Metrics**: Processing statistics and performance metrics
- **Structured Logging**: Comprehensive logging with correlation IDs
- **Graceful Shutdown**: Proper cleanup during service termination

### Performance Features
- **Configurable Polling**: Adjustable polling intervals
- **Parallel Processing**: Optional parallel event processing
- **Connection Pooling**: Efficient database and RabbitMQ connections
- **Cleanup Jobs**: Automatic cleanup of old processed events

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Assessment      │    │ Archive Service  │    │ Outbox          │
│ Service         │───▶│ (Outbox Events)  │───▶│ Publisher       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │ RabbitMQ        │
                                               └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │ Analysis Worker │
                                               └─────────────────┘
```

## Installation

1. **Install Dependencies**
   ```bash
   cd outbox-publisher
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup**
   The outbox table should already exist in your Archive Service database:
   ```sql
   -- This table should already be created by the Archive Service
   SELECT * FROM archive.outbox_events LIMIT 1;
   ```

## Configuration

### Environment Variables

#### Database Configuration
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_archive
DB_USER=postgres
DB_PASSWORD=password
DB_SCHEMA=archive
```

#### RabbitMQ Configuration
```env
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_EXCHANGE=atma_events
RABBITMQ_ROUTING_KEY=assessment.analysis
RABBITMQ_QUEUE=assessment_analysis_queue
```

#### Processing Configuration
```env
OUTBOX_POLL_INTERVAL=5000          # Poll every 5 seconds
OUTBOX_BATCH_SIZE=50               # Process 50 events per batch
OUTBOX_MAX_RETRIES=5               # Max retry attempts
OUTBOX_RETRY_DELAY_BASE=1000       # Base retry delay (ms)
OUTBOX_RETRY_DELAY_MAX=60000       # Max retry delay (ms)
```

#### Monitoring Configuration
```env
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PORT=3003
LOG_LEVEL=info
METRICS_ENABLED=true
```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Docker
```bash
# Build image
docker build -t atma-outbox-publisher .

# Run container
docker run -d \
  --name outbox-publisher \
  --env-file .env \
  atma-outbox-publisher
```

## API Endpoints

### Health Check
```http
GET /health
```
Returns the health status of all components.

### Metrics
```http
GET /metrics
```
Returns processing statistics and performance metrics.

### Readiness
```http
GET /ready
```
Returns readiness status (for Kubernetes probes).

## Monitoring

### Health Indicators
- **Database Connection**: PostgreSQL connectivity
- **RabbitMQ Connection**: Message broker connectivity
- **Archive Service**: HTTP API availability
- **Processing Status**: Event processing health
- **Circuit Breaker**: Failure protection status

### Key Metrics
- **Events Processed**: Total number of successfully published events
- **Events Failed**: Total number of failed events
- **Processing Time**: Average time per event
- **Queue Depth**: Number of pending events
- **Retry Rate**: Percentage of events requiring retries

### Logging
All logs are structured JSON with the following fields:
- `timestamp`: ISO 8601 timestamp
- `level`: Log level (error, warn, info, debug)
- `message`: Human-readable message
- `service`: Always "outbox-publisher"
- `eventId`: Event ID (for event-specific logs)
- `correlationId`: Request correlation ID

## Error Handling

### Retry Strategy
- **Exponential Backoff**: Delays increase exponentially with each retry
- **Max Retries**: Configurable maximum retry attempts
- **Permanent Failures**: Some errors are not retried (validation errors, etc.)

### Circuit Breaker
- **Failure Threshold**: Opens after consecutive failures
- **Timeout**: Automatic recovery after timeout period
- **Half-Open State**: Gradual recovery testing

### Dead Letter Handling
Events that exceed max retries are marked as permanently failed and can be:
- Manually reviewed and reprocessed
- Sent to a dead letter queue
- Archived for analysis

## Troubleshooting

### Common Issues

1. **High Number of Pending Events**
   - Check RabbitMQ connectivity
   - Verify Archive Service availability
   - Review processing configuration

2. **High Failure Rate**
   - Check event payload validation
   - Verify RabbitMQ exchange/queue configuration
   - Review error logs for patterns

3. **Slow Processing**
   - Increase batch size
   - Enable parallel processing
   - Check database performance

### Debugging

1. **Enable Debug Logging**
   ```env
   LOG_LEVEL=debug
   ```

2. **Check Health Endpoints**
   ```bash
   curl http://localhost:3003/health
   curl http://localhost:3003/metrics
   ```

3. **Monitor Database**
   ```sql
   -- Check outbox event counts
   SELECT 
     COUNT(*) as total,
     COUNT(CASE WHEN processed_at IS NOT NULL THEN 1 END) as processed,
     COUNT(CASE WHEN processed_at IS NULL AND failed_at IS NULL THEN 1 END) as pending,
     COUNT(CASE WHEN failed_at IS NOT NULL THEN 1 END) as failed
   FROM archive.outbox_events;
   ```

## Performance Tuning

### Database Optimization
- Ensure proper indexes on outbox table
- Configure connection pooling
- Monitor query performance

### RabbitMQ Optimization
- Use persistent connections
- Configure appropriate prefetch values
- Monitor queue depths

### Application Tuning
- Adjust batch sizes based on load
- Enable parallel processing for high throughput
- Configure appropriate polling intervals

## Security

- **Service Authentication**: Uses API keys for Archive Service communication
- **Network Security**: Secure connections to database and RabbitMQ
- **Input Validation**: Validates all event payloads before publishing
- **Error Sanitization**: Prevents sensitive data leakage in logs

## Contributing

1. Follow the existing code style
2. Add tests for new functionality
3. Update documentation
4. Ensure all health checks pass

## License

MIT License - see LICENSE file for details.
