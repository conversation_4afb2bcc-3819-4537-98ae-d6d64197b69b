/**
 * Database Configuration for Outbox Publisher
 * Connects to the Archive Service database to access outbox events
 */

const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'atma_archive',
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  dialect: 'postgres',
  logging: process.env.NODE_ENV === 'development' ? (msg) => logger.debug(msg) : false,
  pool: {
    max: parseInt(process.env.DB_POOL_MAX || '10'),
    min: parseInt(process.env.DB_POOL_MIN || '2'),
    acquire: parseInt(process.env.DB_POOL_ACQUIRE || '30000'),
    idle: parseInt(process.env.DB_POOL_IDLE || '10000'),
    evict: parseInt(process.env.DB_POOL_EVICT || '5000'),
    handleDisconnects: true,
    retry: {
      max: 3
    }
  },
  define: {
    timestamps: true,
    underscored: true,
    schema: process.env.DB_SCHEMA || 'archive'
  },
  dialectOptions: {
    ssl: process.env.DB_SSL === 'true' ? {
      require: true,
      rejectUnauthorized: false
    } : false,
    connectTimeout: 60000,
    requestTimeout: 30000
  }
};

// Create Sequelize instance
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  config
);

/**
 * Initialize database connection
 */
const initialize = async () => {
  try {
    logger.info('Initializing database connection...');
    
    // Test connection
    await sequelize.authenticate();
    
    logger.info('Database connection established successfully', {
      host: config.host,
      port: config.port,
      database: config.database,
      schema: config.define.schema
    });
    
    return sequelize;
  } catch (error) {
    logger.error('Failed to initialize database connection', {
      error: error.message,
      host: config.host,
      port: config.port,
      database: config.database
    });
    throw error;
  }
};

/**
 * Close database connection
 */
const close = async () => {
  try {
    if (sequelize) {
      await sequelize.close();
      logger.info('Database connection closed successfully');
    }
  } catch (error) {
    logger.error('Error closing database connection', {
      error: error.message
    });
    throw error;
  }
};

/**
 * Health check for database connection
 */
const healthCheck = async () => {
  try {
    await sequelize.authenticate();
    return {
      status: 'healthy',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

module.exports = {
  sequelize,
  initialize,
  close,
  healthCheck,
  config
};
