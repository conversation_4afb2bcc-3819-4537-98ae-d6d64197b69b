/**
 * Logger Configuration for Outbox Publisher
 * Winston-based logging with structured output
 */

const winston = require('winston');
const path = require('path');

// Log level from environment
const logLevel = process.env.LOG_LEVEL || 'info';
const logFormat = process.env.LOG_FORMAT || 'json';
const nodeEnv = process.env.NODE_ENV || 'development';

// Custom format for development
const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

// Production format (JSON)
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    return JSON.stringify({
      timestamp: info.timestamp,
      level: info.level,
      message: info.message,
      service: 'outbox-publisher',
      environment: nodeEnv,
      ...info
    });
  })
);

// Choose format based on environment and configuration
const logFormatToUse = logFormat === 'json' || nodeEnv === 'production' 
  ? productionFormat 
  : developmentFormat;

// Create transports array
const transports = [
  new winston.transports.Console({
    level: logLevel,
    format: logFormatToUse,
    handleExceptions: true,
    handleRejections: true
  })
];

// Add file transport if enabled
if (process.env.LOG_FILE_ENABLED === 'true') {
  const logDir = path.dirname(process.env.LOG_FILE_PATH || './logs/outbox-publisher.log');
  
  // Ensure log directory exists
  const fs = require('fs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  transports.push(
    new winston.transports.File({
      filename: process.env.LOG_FILE_PATH || './logs/outbox-publisher.log',
      level: logLevel,
      format: productionFormat,
      maxsize: process.env.LOG_FILE_MAX_SIZE || '10m',
      maxFiles: parseInt(process.env.LOG_FILE_MAX_FILES || '5'),
      tailable: true,
      handleExceptions: true,
      handleRejections: true
    })
  );
}

// Create logger instance
const logger = winston.createLogger({
  level: logLevel,
  format: logFormatToUse,
  defaultMeta: {
    service: 'outbox-publisher',
    environment: nodeEnv,
    version: process.env.npm_package_version || '1.0.0'
  },
  transports,
  exitOnError: false
});

// Add stream for HTTP request logging (if needed)
logger.stream = {
  write: (message) => {
    logger.info(message.trim());
  }
};

// Add custom methods for structured logging
logger.logOutboxEvent = (level, message, eventData) => {
  logger.log(level, message, {
    eventId: eventData.id,
    aggregateId: eventData.aggregate_id,
    aggregateType: eventData.aggregate_type,
    eventType: eventData.event_type,
    retryCount: eventData.retry_count,
    maxRetries: eventData.max_retries,
    createdAt: eventData.created_at
  });
};

logger.logProcessingStats = (stats) => {
  logger.info('Outbox processing statistics', {
    totalProcessed: stats.processed,
    totalFailed: stats.failed,
    batchSize: stats.batchSize,
    processingTime: stats.processingTime,
    averageTimePerEvent: stats.averageTimePerEvent
  });
};

logger.logRabbitMQOperation = (operation, success, details = {}) => {
  const level = success ? 'info' : 'error';
  logger.log(level, `RabbitMQ ${operation} ${success ? 'successful' : 'failed'}`, {
    operation,
    success,
    exchange: details.exchange,
    routingKey: details.routingKey,
    messageId: details.messageId,
    error: details.error
  });
};

logger.logDatabaseOperation = (operation, success, details = {}) => {
  const level = success ? 'debug' : 'error';
  logger.log(level, `Database ${operation} ${success ? 'successful' : 'failed'}`, {
    operation,
    success,
    table: details.table,
    recordCount: details.recordCount,
    duration: details.duration,
    error: details.error
  });
};

// Handle uncaught exceptions and rejections
if (nodeEnv === 'production') {
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection', {
      reason: reason instanceof Error ? reason.message : reason,
      stack: reason instanceof Error ? reason.stack : undefined,
      promise: promise.toString()
    });
    process.exit(1);
  });
}

module.exports = logger;
