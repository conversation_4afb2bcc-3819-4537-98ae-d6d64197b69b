{"name": "atma-outbox-publisher", "version": "1.0.0", "description": "Outbox Publisher Worker for ATMA - Ensures reliable event publishing from outbox to RabbitMQ", "main": "src/worker.js", "scripts": {"start": "node src/worker.js", "dev": "nodemon src/worker.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["outbox-pattern", "event-publishing", "rabbitmq", "microservices", "atma"], "author": "ATMA Development Team", "license": "MIT", "dependencies": {"amqplib": "^0.10.3", "axios": "^1.6.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "sequelize": "^6.35.0", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/atma-backend.git", "directory": "outbox-publisher"}, "bugs": {"url": "https://github.com/your-org/atma-backend/issues"}, "homepage": "https://github.com/your-org/atma-backend#readme"}