/**
 * RabbitMQ Publisher Service
 * Handles publishing outbox events to RabbitMQ with proper error handling
 */

const rabbitmq = require('../config/rabbitmq');
const logger = require('../utils/logger');

class RabbitMQPublisher {
  constructor() {
    this.publishCount = 0;
    this.failureCount = 0;
    this.lastPublishTime = null;
  }

  /**
   * Initialize the publisher
   */
  async initialize() {
    try {
      await rabbitmq.initialize();
      logger.info('RabbitMQ Publisher initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize RabbitMQ Publisher', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Publish an outbox event to RabbitMQ
   * @param {Object} event - Outbox event to publish
   */
  async publishEvent(event) {
    try {
      // Validate event
      this.validateEvent(event);

      // Prepare message from event payload
      const message = this.prepareMessage(event);

      // Prepare publish options
      const options = this.preparePublishOptions(event);

      // Publish to RabbitMQ
      await rabbitmq.publishMessage(message, event.routing_key, options);

      // Update statistics
      this.publishCount++;
      this.lastPublishTime = new Date();

      logger.logRabbitMQOperation('publish', true, {
        exchange: event.exchange,
        routingKey: event.routing_key,
        messageId: options.messageId,
        eventId: event.id
      });

    } catch (error) {
      this.failureCount++;
      
      logger.logRabbitMQOperation('publish', false, {
        exchange: event.exchange,
        routingKey: event.routing_key,
        messageId: event.id,
        eventId: event.id,
        error: error.message
      });

      // Enhance error message for better debugging
      const enhancedError = new Error(`Failed to publish event ${event.id}: ${error.message}`);
      enhancedError.originalError = error;
      enhancedError.eventId = event.id;
      enhancedError.isRetryable = this.isRetryableError(error);
      
      throw enhancedError;
    }
  }

  /**
   * Validate outbox event before publishing
   * @param {Object} event - Outbox event
   */
  validateEvent(event) {
    if (!event) {
      throw new Error('Event is required');
    }

    if (!event.id) {
      throw new Error('Event ID is required');
    }

    if (!event.payload) {
      throw new Error('Event payload is required');
    }

    if (!event.routing_key) {
      throw new Error('Event routing key is required');
    }

    if (!event.exchange) {
      throw new Error('Event exchange is required');
    }

    // Validate payload structure for assessment events
    if (event.event_type === 'assessment.submitted') {
      this.validateAssessmentPayload(event.payload);
    }
  }

  /**
   * Validate assessment event payload
   * @param {Object} payload - Event payload
   */
  validateAssessmentPayload(payload) {
    const requiredFields = ['jobId', 'userId', 'assessmentData', 'assessmentName'];
    
    for (const field of requiredFields) {
      if (!payload[field]) {
        throw new Error(`Assessment payload missing required field: ${field}`);
      }
    }

    // Validate assessment data structure
    if (!payload.assessmentData.riasec || !payload.assessmentData.ocean || !payload.assessmentData.viaIs) {
      throw new Error('Assessment data missing required components (riasec, ocean, viaIs)');
    }
  }

  /**
   * Prepare message for publishing
   * @param {Object} event - Outbox event
   * @returns {Object} Message to publish
   */
  prepareMessage(event) {
    // For assessment events, use the payload directly as it's already in the correct format
    if (event.event_type === 'assessment.submitted') {
      return {
        ...event.payload,
        // Add metadata from the event
        eventId: event.id,
        eventType: event.event_type,
        eventVersion: event.event_version,
        aggregateId: event.aggregate_id,
        aggregateType: event.aggregate_type,
        originalTimestamp: event.created_at,
        publishedAt: new Date().toISOString()
      };
    }

    // For other event types, wrap the payload
    return {
      eventId: event.id,
      eventType: event.event_type,
      eventVersion: event.event_version,
      aggregateId: event.aggregate_id,
      aggregateType: event.aggregate_type,
      payload: event.payload,
      metadata: event.metadata,
      timestamp: event.created_at,
      publishedAt: new Date().toISOString()
    };
  }

  /**
   * Prepare publish options
   * @param {Object} event - Outbox event
   * @returns {Object} Publish options
   */
  preparePublishOptions(event) {
    return {
      messageId: event.id,
      timestamp: Date.now(),
      headers: {
        eventId: event.id,
        eventType: event.event_type,
        eventVersion: event.event_version,
        aggregateId: event.aggregate_id,
        aggregateType: event.aggregate_type,
        retryCount: event.retry_count,
        originalTimestamp: event.created_at,
        publishedBy: 'outbox-publisher'
      },
      // Set expiration for old events (24 hours)
      expiration: '86400000'
    };
  }

  /**
   * Determine if an error is retryable
   * @param {Error} error - Error to check
   * @returns {Boolean} Whether the error is retryable
   */
  isRetryableError(error) {
    const nonRetryablePatterns = [
      'Invalid payload',
      'Validation error',
      'Schema error',
      'Malformed message',
      'Exchange not found',
      'Queue not found'
    ];

    const retryablePatterns = [
      'Connection failed',
      'Channel closed',
      'Network error',
      'Timeout',
      'ECONNRESET',
      'ENOTFOUND',
      'ETIMEDOUT'
    ];

    const errorMessage = error.message.toLowerCase();

    // Check for non-retryable errors first
    if (nonRetryablePatterns.some(pattern => errorMessage.includes(pattern.toLowerCase()))) {
      return false;
    }

    // Check for explicitly retryable errors
    if (retryablePatterns.some(pattern => errorMessage.includes(pattern.toLowerCase()))) {
      return true;
    }

    // Default to retryable for unknown errors
    return true;
  }

  /**
   * Batch publish multiple events
   * @param {Array} events - Array of outbox events
   * @returns {Object} Batch publish results
   */
  async batchPublish(events) {
    const results = {
      published: 0,
      failed: 0,
      errors: []
    };

    const startTime = Date.now();

    for (const event of events) {
      try {
        await this.publishEvent(event);
        results.published++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          eventId: event.id,
          error: error.message,
          isRetryable: error.isRetryable
        });
      }
    }

    const processingTime = Date.now() - startTime;

    logger.info('Batch publish completed', {
      totalEvents: events.length,
      published: results.published,
      failed: results.failed,
      processingTime,
      averageTimePerEvent: processingTime / events.length
    });

    return results;
  }

  /**
   * Get publisher statistics
   */
  getStats() {
    return {
      publishCount: this.publishCount,
      failureCount: this.failureCount,
      successRate: this.publishCount > 0 ? 
        ((this.publishCount - this.failureCount) / this.publishCount * 100).toFixed(2) + '%' : 
        '0%',
      lastPublishTime: this.lastPublishTime,
      isHealthy: rabbitmq.isConnectionHealthy()
    };
  }

  /**
   * Health check for the publisher
   */
  async healthCheck() {
    try {
      const rabbitmqHealth = await rabbitmq.healthCheck();
      const stats = this.getStats();

      return {
        status: rabbitmqHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
        rabbitmq: rabbitmqHealth,
        publisher: stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Close the publisher
   */
  async close() {
    try {
      await rabbitmq.close();
      logger.info('RabbitMQ Publisher closed successfully');
    } catch (error) {
      logger.error('Error closing RabbitMQ Publisher', {
        error: error.message
      });
      throw error;
    }
  }
}

// Create singleton instance
const rabbitmqPublisher = new RabbitMQPublisher();

module.exports = rabbitmqPublisher;
