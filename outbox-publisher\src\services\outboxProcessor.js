/**
 * Outbox Processor Service
 * Core logic for processing outbox events and publishing to RabbitMQ
 */

const OutboxEvent = require('../models/OutboxEvent');
const rabbitmqPublisher = require('./rabbitmqPublisher');
const archiveServiceClient = require('./archiveServiceClient');
const logger = require('../utils/logger');

class OutboxProcessor {
  constructor() {
    this.isProcessing = false;
    this.consecutiveFailures = 0;
    this.maxConsecutiveFailures = parseInt(process.env.MAX_CONSECUTIVE_FAILURES || '10');
    this.circuitBreakerTimeout = parseInt(process.env.CIRCUIT_BREAKER_TIMEOUT || '60000');
    this.lastCircuitBreakerOpen = null;
    this.processingStats = {
      totalProcessed: 0,
      totalFailed: 0,
      lastProcessingTime: null,
      averageProcessingTime: 0
    };
  }

  /**
   * Start processing outbox events
   */
  async start() {
    const pollInterval = parseInt(process.env.OUTBOX_POLL_INTERVAL || '5000');
    
    logger.info('Starting outbox processor', {
      pollInterval,
      batchSize: process.env.OUTBOX_BATCH_SIZE,
      maxRetries: process.env.OUTBOX_MAX_RETRIES
    });

    // Start the main processing loop
    this.processingInterval = setInterval(async () => {
      if (!this.isProcessing && !this.isCircuitBreakerOpen()) {
        await this.processOutboxEvents();
      }
    }, pollInterval);

    // Start cleanup interval
    const cleanupInterval = parseInt(process.env.OUTBOX_CLEANUP_INTERVAL || '3600000'); // 1 hour
    this.cleanupInterval = setInterval(async () => {
      await this.cleanupOldEvents();
    }, cleanupInterval);

    logger.info('Outbox processor started successfully');
  }

  /**
   * Stop processing outbox events
   */
  async stop() {
    logger.info('Stopping outbox processor...');
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Wait for current processing to complete
    while (this.isProcessing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    logger.info('Outbox processor stopped');
  }

  /**
   * Process outbox events in batches
   */
  async processOutboxEvents() {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;
    const startTime = Date.now();
    
    try {
      // Process unprocessed events
      await this.processUnprocessedEvents();
      
      // Process retryable events
      await this.processRetryableEvents();
      
      // Reset consecutive failures on success
      this.consecutiveFailures = 0;
      
    } catch (error) {
      this.consecutiveFailures++;
      logger.error('Error in outbox processing cycle', {
        error: error.message,
        consecutiveFailures: this.consecutiveFailures,
        maxConsecutiveFailures: this.maxConsecutiveFailures
      });

      // Open circuit breaker if too many consecutive failures
      if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
        this.openCircuitBreaker();
      }
    } finally {
      const processingTime = Date.now() - startTime;
      this.updateProcessingStats(processingTime);
      this.isProcessing = false;
    }
  }

  /**
   * Process unprocessed events
   */
  async processUnprocessedEvents() {
    const batchSize = parseInt(process.env.OUTBOX_BATCH_SIZE || '50');
    
    try {
      const events = await OutboxEvent.getUnprocessedEvents({ limit: batchSize });
      
      if (events.length === 0) {
        logger.debug('No unprocessed events found');
        return;
      }

      logger.info('Processing unprocessed events', { count: events.length });
      
      const results = await this.processBatch(events);
      
      logger.logProcessingStats({
        processed: results.processed,
        failed: results.failed,
        batchSize: events.length,
        processingTime: results.processingTime,
        averageTimePerEvent: results.processingTime / events.length
      });
      
    } catch (error) {
      logger.error('Failed to process unprocessed events', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Process retryable events
   */
  async processRetryableEvents() {
    const batchSize = parseInt(process.env.OUTBOX_BATCH_SIZE || '50');
    const minRetryDelay = parseInt(process.env.OUTBOX_RETRY_DELAY_BASE || '1000');
    
    try {
      const events = await OutboxEvent.getRetryableEvents({ 
        limit: batchSize,
        minRetryDelay 
      });
      
      if (events.length === 0) {
        logger.debug('No retryable events found');
        return;
      }

      logger.info('Processing retryable events', { count: events.length });
      
      const results = await this.processBatch(events);
      
      logger.info('Retryable events processing completed', {
        processed: results.processed,
        failed: results.failed,
        totalEvents: events.length
      });
      
    } catch (error) {
      logger.error('Failed to process retryable events', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Process a batch of events
   */
  async processBatch(events) {
    const startTime = Date.now();
    const results = {
      processed: 0,
      failed: 0,
      errors: []
    };

    const enableParallelProcessing = process.env.ENABLE_PARALLEL_PROCESSING === 'true';
    
    if (enableParallelProcessing) {
      // Process events in parallel
      const promises = events.map(event => this.processEvent(event));
      const eventResults = await Promise.allSettled(promises);
      
      eventResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.processed++;
        } else {
          results.failed++;
          results.errors.push({
            eventId: events[index].id,
            error: result.reason.message
          });
        }
      });
    } else {
      // Process events sequentially
      for (const event of events) {
        try {
          await this.processEvent(event);
          results.processed++;
        } catch (error) {
          results.failed++;
          results.errors.push({
            eventId: event.id,
            error: error.message
          });
        }
      }
    }

    results.processingTime = Date.now() - startTime;
    return results;
  }

  /**
   * Process a single event
   */
  async processEvent(event) {
    try {
      logger.logOutboxEvent('debug', 'Processing outbox event', event);

      // Publish to RabbitMQ
      await rabbitmqPublisher.publishEvent(event);

      // Mark as processed
      await archiveServiceClient.markEventAsProcessed(event.id);

      logger.logOutboxEvent('info', 'Outbox event processed successfully', event);
      
      this.processingStats.totalProcessed++;
      
    } catch (error) {
      logger.logOutboxEvent('error', 'Failed to process outbox event', event);
      
      // Determine if this is a permanent failure
      const isPermanentFailure = this.isPermanentFailure(error);
      
      // Mark as failed (with or without retry)
      await archiveServiceClient.markEventAsFailed(
        event.id, 
        error.message, 
        !isPermanentFailure
      );
      
      this.processingStats.totalFailed++;
      throw error;
    }
  }

  /**
   * Determine if an error is a permanent failure
   */
  isPermanentFailure(error) {
    const permanentErrorPatterns = [
      'Invalid payload',
      'Validation error',
      'Schema error',
      'Malformed event',
      'Event not found'
    ];

    return permanentErrorPatterns.some(pattern =>
      error.message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Check if circuit breaker is open
   */
  isCircuitBreakerOpen() {
    if (!this.lastCircuitBreakerOpen) {
      return false;
    }

    const timeSinceOpen = Date.now() - this.lastCircuitBreakerOpen;
    if (timeSinceOpen >= this.circuitBreakerTimeout) {
      this.closeCircuitBreaker();
      return false;
    }

    return true;
  }

  /**
   * Open circuit breaker
   */
  openCircuitBreaker() {
    this.lastCircuitBreakerOpen = Date.now();
    logger.warn('Circuit breaker opened due to consecutive failures', {
      consecutiveFailures: this.consecutiveFailures,
      timeout: this.circuitBreakerTimeout
    });
  }

  /**
   * Close circuit breaker
   */
  closeCircuitBreaker() {
    this.lastCircuitBreakerOpen = null;
    this.consecutiveFailures = 0;
    logger.info('Circuit breaker closed, resuming processing');
  }

  /**
   * Update processing statistics
   */
  updateProcessingStats(processingTime) {
    this.processingStats.lastProcessingTime = processingTime;

    // Calculate rolling average
    if (this.processingStats.averageProcessingTime === 0) {
      this.processingStats.averageProcessingTime = processingTime;
    } else {
      this.processingStats.averageProcessingTime =
        (this.processingStats.averageProcessingTime * 0.9) + (processingTime * 0.1);
    }
  }

  /**
   * Clean up old processed events
   */
  async cleanupOldEvents() {
    try {
      const olderThanDays = parseInt(process.env.OUTBOX_CLEANUP_OLDER_THAN_DAYS || '30');

      logger.info('Starting outbox cleanup', { olderThanDays });

      const deletedCount = await archiveServiceClient.cleanupOldEvents(olderThanDays);

      logger.info('Outbox cleanup completed', { deletedCount });

    } catch (error) {
      logger.error('Failed to cleanup old events', {
        error: error.message
      });
    }
  }

  /**
   * Get current processing statistics
   */
  getProcessingStats() {
    return {
      ...this.processingStats,
      isProcessing: this.isProcessing,
      consecutiveFailures: this.consecutiveFailures,
      circuitBreakerOpen: this.isCircuitBreakerOpen(),
      lastCircuitBreakerOpen: this.lastCircuitBreakerOpen
    };
  }

  /**
   * Get health status
   */
  async getHealthStatus() {
    try {
      const eventCounts = await OutboxEvent.getEventCounts();
      const oldestAge = await OutboxEvent.getOldestUnprocessedAge();

      const issues = [];

      // Check for high number of pending events
      if (eventCounts.pending > 1000) {
        issues.push(`High number of pending events: ${eventCounts.pending}`);
      }

      // Check for high failure rate
      const totalEvents = eventCounts.processed + eventCounts.failed;
      if (totalEvents > 0 && (eventCounts.failed / totalEvents) > 0.1) {
        issues.push(`High failure rate: ${((eventCounts.failed / totalEvents) * 100).toFixed(1)}%`);
      }

      // Check for old unprocessed events
      if (oldestAge && oldestAge > 30) {
        issues.push(`Oldest unprocessed event is ${Math.round(oldestAge)} minutes old`);
      }

      // Check circuit breaker status
      if (this.isCircuitBreakerOpen()) {
        issues.push('Circuit breaker is open');
      }

      return {
        healthy: issues.length === 0,
        issues,
        eventCounts,
        oldestUnprocessedAge: oldestAge,
        processingStats: this.getProcessingStats(),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        healthy: false,
        issues: [`Health check failed: ${error.message}`],
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = OutboxProcessor;
