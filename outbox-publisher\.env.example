# Outbox Publisher Configuration

# Environment
NODE_ENV=development

# Database Configuration (Archive Service Database)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_archive
DB_USER=postgres
DB_PASSWORD=password
DB_SCHEMA=archive
DB_SSL=false

# Database Pool Configuration
DB_POOL_MAX=10
DB_POOL_MIN=2
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_EXCHANGE=atma_events
RABBITMQ_ROUTING_KEY=assessment.analysis
RABBITMQ_QUEUE=assessment_analysis_queue

# RabbitMQ Connection Options
RABBITMQ_HEARTBEAT=60
RABBITMQ_CONNECTION_TIMEOUT=10000
RABBITMQ_RECONNECT_DELAY=5000

# Outbox Processing Configuration
OUTBOX_POLL_INTERVAL=5000
OUTBOX_BATCH_SIZE=50
OUTBOX_MAX_RETRIES=5
OUTBOX_RETRY_DELAY_BASE=1000
OUTBOX_RETRY_DELAY_MAX=60000
OUTBOX_CLEANUP_INTERVAL=3600000
OUTBOX_CLEANUP_OLDER_THAN_DAYS=30

# Worker Configuration
WORKER_CONCURRENCY=5
WORKER_SHUTDOWN_TIMEOUT=30000

# Monitoring and Health
HEALTH_CHECK_PORT=3003
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
LOG_LEVEL=info

# Archive Service Configuration (for API calls)
ARCHIVE_SERVICE_URL=http://localhost:3002
ARCHIVE_SERVICE_API_KEY=your-service-api-key
ARCHIVE_SERVICE_TIMEOUT=10000

# Error Handling
MAX_CONSECUTIVE_FAILURES=10
CIRCUIT_BREAKER_TIMEOUT=60000
DEAD_LETTER_ENABLED=true

# Performance Tuning
ENABLE_BATCH_PROCESSING=true
ENABLE_PARALLEL_PROCESSING=true
PROCESSING_TIMEOUT=30000

# Logging Configuration
LOG_FORMAT=json
LOG_FILE_ENABLED=false
LOG_FILE_PATH=./logs/outbox-publisher.log
LOG_FILE_MAX_SIZE=10m
LOG_FILE_MAX_FILES=5
